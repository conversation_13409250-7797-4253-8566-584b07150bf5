// Example of getting a URL to an extension resource
const warningPageUrl = chrome.runtime.getURL('warning.html');

let warningOverlay = null;
let timerId = null;
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('[Content Script] Received message:', message); // Added log

    if (message.action === 'showWarning') {
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'warning_overlay_shown', eventParams: { reason: message.reason, page_location: window.location.href } });
        showWarningOverlay(message.reason);
    } else if (message.action === 'redirectWarning') {
        window.location.href = chrome.runtime.getURL('warning.html');
    } else if (message.action === 'startTimer' && message.duration) {
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'game_timer_shown', eventParams: { duration_ms: message.duration, page_location: window.location.href } });
        showTimerNotification(message.duration);
    } else if (message.action === 'showEyeReminder') {
         chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'eye_reminder_shown', eventParams: { page_location: window.location.href } });
        showEyeReminder();
    }
});

function showWarningOverlay(reason) {
    if (warningOverlay) {
        document.body.removeChild(warningOverlay);
    }

    warningOverlay = document.createElement('div');
    warningOverlay.className = 'safety-warning-overlay';
    // Overlay styles
    warningOverlay.style.position = 'fixed';
    warningOverlay.style.top = '0';
    warningOverlay.style.left = '0';
    warningOverlay.style.width = '100%';
    warningOverlay.style.height = '100%';
    warningOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    warningOverlay.style.display = 'flex';
    warningOverlay.style.justifyContent = 'center';
    warningOverlay.style.alignItems = 'center';
    warningOverlay.style.zIndex = '10000';
    warningOverlay.style.backdropFilter = 'blur(2px)';

    // Content container
    const contentContainer = document.createElement('div');
    contentContainer.style.backgroundColor = '#f8f9fa';
    contentContainer.style.backgroundImage = 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%234285f4\' fill-opacity=\'0.15\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")';
    contentContainer.style.padding = '30px';
    contentContainer.style.borderRadius = '10px';
    contentContainer.style.textAlign = 'center';
    contentContainer.style.maxWidth = '600px';
    contentContainer.style.width = '80%';
    contentContainer.style.boxShadow = '0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05)';
    contentContainer.style.border = '1px solid #f1f3f4';

    // Warning icon
    const warningIcon = document.createElement('div');
    warningIcon.style.width = '80px';
    warningIcon.style.height = '80px';
    warningIcon.style.margin = '0 auto 30px';
    warningIcon.style.background = 'linear-gradient(135deg, #4285f4 0%, #3367d6 100%)';
    warningIcon.style.borderRadius = '50%';
    warningIcon.style.display = 'flex';
    warningIcon.style.alignItems = 'center';
    warningIcon.style.justifyContent = 'center';
    warningIcon.style.color = 'white';
    warningIcon.style.fontSize = '40px';
    warningIcon.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
    warningIcon.innerHTML = '⚠';

    // Labels
    const titleLabel = document.createElement('h1');
    titleLabel.textContent = 'This webpage may need to be monitored for minors.';
    titleLabel.style.fontSize = '1.5em';
    titleLabel.style.color = '#4285f4';
    titleLabel.style.marginBottom = '20px';
    titleLabel.style.fontFamily = "'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif";
    titleLabel.style.fontWeight = '600';

    const warningBox = document.createElement('div');
    warningBox.style.backgroundColor = '#e8f0fe';
    warningBox.style.borderRadius = '10px';
    warningBox.style.padding = '25px';
    warningBox.style.margin = '20px 0';
    warningBox.style.textAlign = 'center';
    warningBox.style.border = '1px solid rgba(66, 133, 244, 0.2)';
    warningBox.style.boxShadow = '0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05)';
    warningBox.style.backgroundImage = 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z\' fill=\'%234285f4\' fill-opacity=\'0.15\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")';

    const warningTitle = document.createElement('h2');
    warningTitle.textContent = 'Warning';
    warningTitle.style.color = '#4285f4';
    warningTitle.style.fontSize = '1.8em';
    warningTitle.style.marginBottom = '20px';
    warningTitle.style.textTransform = 'uppercase';
    warningTitle.style.letterSpacing = '1px';
    warningTitle.style.fontFamily = "'Arial', 'Helvetica Neue', sans-serif";
    warningTitle.style.fontWeight = '600';

    const reasonLabel = document.createElement('p');
    reasonLabel.textContent = `Reason: ${reason}`;
    reasonLabel.style.fontSize = '1.2em';
    reasonLabel.style.color = '#202124';
    reasonLabel.style.marginBottom = '0';
    reasonLabel.style.lineHeight = '1.8';

    // Buttons container
    const buttonsContainer = document.createElement('div');
    buttonsContainer.style.display = 'flex';
    buttonsContainer.style.justifyContent = 'space-between';
    buttonsContainer.style.marginTop = '30px';

    // Leave button
    const leaveButton = document.createElement('button');
    leaveButton.id = 'leaveButton';
    leaveButton.textContent = 'Leave this page';
    leaveButton.style.padding = '12px 20px';
    leaveButton.style.background = 'linear-gradient(135deg, #db4437 0%, #ef5350 100%)';
    leaveButton.style.color = '#fff';
    leaveButton.style.border = 'none';
    leaveButton.style.borderRadius = '8px';
    leaveButton.style.cursor = 'pointer';
    leaveButton.style.flex = '1';
    leaveButton.style.marginRight = '10px';
    leaveButton.style.fontWeight = '500';
    leaveButton.style.transition = 'all 0.2s ease';
    leaveButton.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
    leaveButton.style.fontFamily = "'Arial', 'Helvetica Neue', sans-serif";

    leaveButton.onmouseover = function() {
        this.style.transform = 'translateY(-1px)';
        this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
    };

    leaveButton.onmouseout = function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
    };

    // Continue button
    const continueButton = document.createElement('button');
    continueButton.id = 'continueButton';
    continueButton.textContent = 'Continue browsing';
    continueButton.style.padding = '12px 20px';
    continueButton.style.background = 'linear-gradient(135deg, #4285f4 0%, #3367d6 100%)';
    continueButton.style.color = '#fff';
    continueButton.style.border = 'none';
    continueButton.style.borderRadius = '8px';
    continueButton.style.cursor = 'pointer';
    continueButton.style.flex = '1';
    continueButton.style.marginLeft = '10px';
    continueButton.style.fontWeight = '500';
    continueButton.style.transition = 'all 0.2s ease';
    continueButton.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
    continueButton.style.fontFamily = "'Arial', 'Helvetica Neue', sans-serif";

    continueButton.onmouseover = function() {
        this.style.transform = 'translateY(-1px)';
        this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
    };

    continueButton.onmouseout = function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
    };

    // Append elements
    warningBox.appendChild(warningTitle);
    warningBox.appendChild(reasonLabel);

    // Append buttons to container
    buttonsContainer.appendChild(leaveButton);
    buttonsContainer.appendChild(continueButton);

    // Append all elements to content container
    contentContainer.appendChild(warningIcon);
    contentContainer.appendChild(titleLabel);
    contentContainer.appendChild(warningBox);
    contentContainer.appendChild(buttonsContainer);

    // Append content to overlay
    warningOverlay.appendChild(contentContainer);

    // Append overlay to body
    document.body.appendChild(warningOverlay);

    // Event listeners
    leaveButton.addEventListener('click', () => {
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'warning_overlay_leave_clicked', eventParams: { page_location: window.location.href } });
        window.location.href = 'about:blank';
    });

    continueButton.addEventListener('click', () => {
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'warning_overlay_continue_clicked', eventParams: { page_location: window.location.href } });
        document.body.removeChild(warningOverlay);
    });
}

function showAlert(message) {
    const alertDiv = document.createElement('div');
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '50%';
    alertDiv.style.left = '50%';
    alertDiv.style.transform = 'translate(-50%, -50%)';
    alertDiv.style.backgroundColor = '#f8f9fa';
    alertDiv.style.backgroundImage = 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%234285f4\' fill-opacity=\'0.15\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")';
    alertDiv.style.color = '#202124';
    alertDiv.style.padding = '20px';
    alertDiv.style.zIndex = '10000';
    alertDiv.style.textAlign = 'center';
    alertDiv.style.borderRadius = '10px';
    alertDiv.style.boxShadow = '0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05)';
    alertDiv.style.border = '1px solid rgba(66, 133, 244, 0.2)';
    alertDiv.style.fontFamily = "'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif";
    alertDiv.style.maxWidth = '400px';
    alertDiv.style.width = '80%';

    // Create alert icon
    const alertIcon = document.createElement('div');
    alertIcon.style.width = '50px';
    alertIcon.style.height = '50px';
    alertIcon.style.margin = '0 auto 15px';
    alertIcon.style.background = 'linear-gradient(135deg, #4285f4 0%, #3367d6 100%)';
    alertIcon.style.borderRadius = '50%';
    alertIcon.style.display = 'flex';
    alertIcon.style.alignItems = 'center';
    alertIcon.style.justifyContent = 'center';
    alertIcon.style.color = 'white';
    alertIcon.style.fontSize = '30px';
    alertIcon.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
    alertIcon.innerHTML = '!';

    // Create message text
    const messageText = document.createElement('p');
    messageText.textContent = message;
    messageText.style.margin = '0';
    messageText.style.fontSize = '16px';
    messageText.style.lineHeight = '1.5';

    // Append elements
    alertDiv.appendChild(alertIcon);
    alertDiv.appendChild(messageText);

    document.body.appendChild(alertDiv);

    // Add fade-in animation
    alertDiv.style.opacity = '0';
    alertDiv.style.transition = 'opacity 0.3s ease';
    setTimeout(() => {
        alertDiv.style.opacity = '1';
    }, 10);

    // Remove with fade-out animation
    setTimeout(() => {
        alertDiv.style.opacity = '0';
        setTimeout(() => {
            if (alertDiv.parentNode) {
                document.body.removeChild(alertDiv);
            }
        }, 300);
    }, 5000); // Display the alert for 5 seconds
}

function showTimerNotification(duration) {
    // Remove existing notification if any
    const existingNotification = document.getElementById('safety-timer-notification');
    if (existingNotification) {
        document.body.removeChild(existingNotification);
    }

    // Create notification banner with fixed size and enhanced color palette
    const notification = document.createElement('div');
    notification.id = 'safety-timer-notification';
    notification.style.position = 'fixed';
    notification.style.top = '20px'; // Initial top position
    notification.style.left = '20px'; // Initial left position
    notification.style.width = '220px'; // Fixed width
    notification.style.height = '50px'; // Fixed height to accommodate text
    notification.style.backgroundColor = '#f8f9fa'; // Match popup container background
    notification.style.backgroundImage = 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%234285f4\' fill-opacity=\'0.15\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")';
    notification.style.color = '#202124'; // Match popup text color
    notification.style.border = '1px solid rgba(66, 133, 244, 0.2)'; // Lighter border with opacity
    notification.style.borderRadius = '10px'; // Match popup border radius
    notification.style.boxShadow = '0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05)'; // Match popup box shadow
    notification.style.padding = '10px 20px';
    notification.style.zIndex = '10000';
    notification.style.fontSize = '14px'; // Match popup font size
    notification.style.fontWeight = '500';
    notification.style.display = 'flex';
    notification.style.alignItems = 'center';
    notification.style.cursor = 'move'; // Indicate that the timer is draggable
    notification.style.userSelect = 'none'; // Prevent text selection during drag
    notification.style.overflow = 'hidden'; // Prevent content from stretching
    notification.style.fontFamily = "'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif";

    let remainingTime = duration / 1000; // in seconds
    const minutes = Math.floor(remainingTime / 60);
    const seconds = remainingTime % 60;
    const initialTimeString = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;

    // Create a clock icon
    const clockIcon = document.createElement('div');
    clockIcon.style.width = '24px';
    clockIcon.style.height = '24px';
    clockIcon.style.borderRadius = '50%';
    clockIcon.style.background = 'linear-gradient(135deg, #4285f4 0%, #3367d6 100%)';
    clockIcon.style.color = 'white';
    clockIcon.style.display = 'flex';
    clockIcon.style.alignItems = 'center';
    clockIcon.style.justifyContent = 'center';
    clockIcon.style.marginRight = '10px';
    clockIcon.style.flexShrink = '0';
    clockIcon.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
    clockIcon.innerHTML = '⏱️';

    // Create timer text container
    const timerTextContainer = document.createElement('div');
    timerTextContainer.style.flexGrow = '1';

    // Create label
    const timerLabel = document.createElement('span');
    timerLabel.textContent = 'Time Remaining: ';
    timerLabel.style.fontWeight = '500';

    // Create timer display
    const timerDisplay = document.createElement('span');
    timerDisplay.id = 'timer';
    timerDisplay.textContent = initialTimeString;
    timerDisplay.style.fontWeight = '700';
    timerDisplay.style.color = '#4285f4';

    // Assemble the notification
    timerTextContainer.appendChild(timerLabel);
    timerTextContainer.appendChild(timerDisplay);
    notification.appendChild(clockIcon);
    notification.appendChild(timerTextContainer);

    document.body.appendChild(notification);

    // Add entrance animation
    notification.style.opacity = '0';
    notification.style.transform = 'translateY(-20px)';
    notification.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';
    }, 10);

    // Make the notification draggable
    makeElementDraggable(notification);

    const timerElement = document.getElementById('timer');

    const countdownInterval = setInterval(() => {
        remainingTime--;
        const minutes = Math.floor(remainingTime / 60);
        const seconds = remainingTime % 60;
        timerElement.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;

        // Add pulsing effect when time is running low (less than 30 seconds)
        if (remainingTime <= 30) {
            timerElement.style.animation = 'pulse 1s infinite';
            timerElement.style.color = '#4285f4';

            // Add the pulse animation if it doesn't exist
            if (!document.getElementById('pulse-animation')) {
                const style = document.createElement('style');
                style.id = 'pulse-animation';
                style.textContent = `
                    @keyframes pulse {
                        0% { opacity: 1; }
                        50% { opacity: 0.5; }
                        100% { opacity: 1; }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        if (remainingTime <= 0) {
            clearInterval(countdownInterval);
            chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'game_timer_expired_site_blocked', eventParams: { page_location: window.location.href } });

            // Fade out animation
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';

            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
                // Redirect to closed.html
                window.location.href = chrome.runtime.getURL('closed.html');
            }, 300);

            // Inform background to block the site - Note: background.js currently doesn't handle 'blockSite' message
            // chrome.runtime.sendMessage({ action: 'blockSite', url: window.location.href }); // Keep if planning to implement in background.js
        }
    }, 1000);
}

function makeElementDraggable(elmnt) {
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

    // Use modern event listeners instead of direct assignment
    elmnt.addEventListener('mousedown', dragMouseDown);

    function dragMouseDown(e) {
        e.preventDefault();
        // Get the mouse cursor position at startup:
        pos3 = e.clientX;
        pos4 = e.clientY;

        // Use modern event listeners
        document.addEventListener('mouseup', closeDragElement);
        document.addEventListener('mousemove', elementDrag);
    }

    function elementDrag(e) {
        e.preventDefault();
        // Calculate the new cursor position:
        pos1 = pos3 - e.clientX;
        pos2 = pos4 - e.clientY;
        pos3 = e.clientX;
        pos4 = e.clientY;
        // Set the element's new position:
        elmnt.style.top = (elmnt.offsetTop - pos2) + "px";
        elmnt.style.left = (elmnt.offsetLeft - pos1) + "px";
    }

    function closeDragElement() {
        // Stop moving when mouse button is released:
        document.removeEventListener('mouseup', closeDragElement);
        document.removeEventListener('mousemove', elementDrag);
    }
}

function showEyeReminder() {
    console.log('[Content Script] Executing showEyeReminder function.'); // Added log
    // Create the overlay
    const eyeOverlay = document.createElement('div');
    eyeOverlay.id = 'eye-reminder-overlay';
    eyeOverlay.style.position = 'fixed';
    eyeOverlay.style.top = '0';
    eyeOverlay.style.left = '0';
    eyeOverlay.style.width = '100%';
    eyeOverlay.style.height = '100%';
    eyeOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.90)'; // Increased opacity
    eyeOverlay.style.display = 'flex';
    eyeOverlay.style.flexDirection = 'column';
    eyeOverlay.style.justifyContent = 'center';
    eyeOverlay.style.alignItems = 'center';
    eyeOverlay.style.zIndex = '10001'; // Ensure it's above other overlays
    eyeOverlay.style.backdropFilter = 'blur(2px)';

    // Create container for eye reminder content
    const reminderContainer = document.createElement('div');
    reminderContainer.style.backgroundColor = '#f8f9fa';
    reminderContainer.style.backgroundImage = 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z\' fill=\'%234285f4\' fill-opacity=\'0.15\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")';
    reminderContainer.style.padding = '40px';
    reminderContainer.style.borderRadius = '10px';
    reminderContainer.style.boxShadow = '0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05)';
    reminderContainer.style.textAlign = 'center';
    reminderContainer.style.maxWidth = '500px';
    reminderContainer.style.width = '90%';
    reminderContainer.style.border = '1px solid #f1f3f4';

    // Create title
    const title = document.createElement('h2');
    title.textContent = 'Time to Rest Your Eyes!';
    title.style.color = '#4285f4';
    title.style.fontSize = '24px';
    title.style.marginBottom = '20px';
    title.style.fontFamily = "'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif";
    title.style.fontWeight = '600';

    // Create eye animation container
    const eyeContainer = document.createElement('div');
    eyeContainer.id = 'blinking-eye';
    eyeContainer.style.width = '150px';
    eyeContainer.style.height = '150px';
    eyeContainer.style.margin = '0 auto 30px';

    // Add the SVG eye animation
    eyeContainer.innerHTML = `
        <svg viewBox="0 0 64 64" width="100%" height="100%">
            <!-- Realistic Blinking Eye SVG -->
            <g>
                <!-- Eye Outline -->
                <path d="M32 12C20 12 10.67 20.5 10.67 32S20 52 32 52s21.33-8.5 21.33-20S44 12 32 12z"
                      stroke="#4285f4" stroke-width="4" fill="none"/>
                <!-- Upper Eyelid -->
                <path id="upper-lid" d="M10.67 32C15 22 25 18 32 18s17 4 21.33 14"
                      stroke="#4285f4" stroke-width="4" fill="none">
                    <animate
                        attributeName="d"
                        dur="1.5s"
                        repeatCount="indefinite"
                        values="
                            M10.67 32C15 22 25 18 32 18s17 4 21.33 14;
                            M10.67 32C15 28 25 24 32 24s17 4 21.33 8;
                            M10.67 32C15 22 25 18 32 18s17 4 21.33 14
                        "
                    />
                </path>
                <!-- Lower Eyelid -->
                <path id="lower-lid" d="M10.67 32C15 42 25 46 32 46s17-4 21.33-14"
                      stroke="#4285f4" stroke-width="4" fill="none">
                    <animate
                        attributeName="d"
                        dur="1.5s"
                        repeatCount="indefinite"
                        values="
                            M10.67 32C15 42 25 46 32 46s17-4 21.33-14;
                            M10.67 32C15 36 25 40 32 40s17-4 21.33-8;
                            M10.67 32C15 42 25 46 32 46s17-4 21.33-14
                        "
                    />
                </path>
                <!-- Pupil -->
                <circle cx="32" cy="32" r="6" fill="#4285f4">
                    <animate
                        attributeName="r"
                        values="6;6;3;6;6"
                        dur="1.5s"
                        repeatCount="indefinite"
                        keyTimes="0;0.45;0.55;1"
                    />
                </circle>
            </g>
        </svg>
    `;

    // Create message box
    const messageBox = document.createElement('div');
    messageBox.style.backgroundColor = '#e3f2fd';
    messageBox.style.borderRadius = '10px';
    messageBox.style.padding = '20px';
    messageBox.style.marginBottom = '20px';
    messageBox.style.border = '1px solid rgba(66, 133, 244, 0.2)';
    messageBox.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';

    const message = document.createElement('p');
    message.textContent = 'Look away from your screen for 20 seconds and focus on something at least 20 feet away. This helps reduce eye strain and fatigue.';
    message.style.margin = '0';
    message.style.fontSize = '16px';
    message.style.lineHeight = '1.5';
    message.style.color = '#202124';
    message.style.fontFamily = "'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif";

    messageBox.appendChild(message);

    // Assemble the container
    reminderContainer.appendChild(title);
    reminderContainer.appendChild(eyeContainer);
    reminderContainer.appendChild(messageBox);

    // Add subscription reminder if trial expired
    chrome.storage.sync.get(['trialExpired', 'subscribed'], (data) => {
        if (data.trialExpired && !data.subscribed) {
            const subscribeReminder = document.createElement('div');
            subscribeReminder.style.marginTop = '20px';

            const reminderText = document.createElement('p');
            reminderText.textContent = 'Subscribe now to unlock full features!';
            reminderText.style.fontSize = '16px';
            reminderText.style.color = '#202124';
            reminderText.style.marginBottom = '15px';
            reminderText.style.fontFamily = "'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif";

            const subscribeButton = document.createElement('button');
            subscribeButton.id = 'subscribeButton';
            subscribeButton.textContent = 'Subscribe Now';
            subscribeButton.style.padding = '12px 20px';
            subscribeButton.style.background = 'linear-gradient(135deg, #5e35b1 0%, #4527a0 100%)';
            subscribeButton.style.color = '#fff';
            subscribeButton.style.border = 'none';
            subscribeButton.style.borderRadius = '8px';
            subscribeButton.style.cursor = 'pointer';
            subscribeButton.style.fontWeight = '500';
            subscribeButton.style.transition = 'all 0.2s ease';
            subscribeButton.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
            subscribeButton.style.fontFamily = "'Arial', 'Helvetica Neue', sans-serif";

            subscribeButton.onmouseover = function() {
                this.style.transform = 'translateY(-1px)';
                this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
            };

            subscribeButton.onmouseout = function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
            };

            subscribeReminder.appendChild(reminderText);
            subscribeReminder.appendChild(subscribeButton);
            reminderContainer.appendChild(subscribeReminder);

            // Add event listener after the button is added to the DOM
            setTimeout(() => {
                const button = document.getElementById('subscribeButton');
                if (button) {
                    button.addEventListener('click', () => {
                        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'eye_reminder_subscribe_clicked', eventParams: { page_location: window.location.href } });
                        window.open(chrome.runtime.getURL('subscribe.html'), '_blank');
                    });
                }
            }, 0);
        }
    });

    // Add countdown timer
    const countdownContainer = document.createElement('div');
    countdownContainer.style.marginTop = '20px';
    countdownContainer.style.fontSize = '14px';
    countdownContainer.style.color = '#5f6368';
    countdownContainer.style.fontFamily = "'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif";

    // Create countdown with live updating number
    let remainingSeconds = 20;
    const countdownText = document.createElement('span');
    countdownText.textContent = 'This reminder will close in ';

    const countdownNumber = document.createElement('span');
    countdownNumber.style.fontWeight = 'bold';
    countdownNumber.style.color = '#4285f4';
    countdownNumber.style.fontSize = '16px';
    countdownNumber.textContent = remainingSeconds;

    const countdownSuffix = document.createElement('span');
    countdownSuffix.textContent = ' seconds';

    countdownContainer.appendChild(countdownText);
    countdownContainer.appendChild(countdownNumber);
    countdownContainer.appendChild(countdownSuffix);

    // Update countdown every second
    const eyeCountdownInterval = setInterval(() => {
        remainingSeconds--;
        countdownNumber.textContent = remainingSeconds;

        // Add pulse effect when getting close to end
        if (remainingSeconds <= 5) {
            countdownNumber.style.animation = 'pulse 0.5s infinite';
            countdownNumber.style.color = '#db4437';
        }

        if (remainingSeconds <= 0) {
            clearInterval(eyeCountdownInterval);
        }
    }, 1000);

    reminderContainer.appendChild(countdownContainer);

    // Add the container to the overlay
    eyeOverlay.appendChild(reminderContainer);

    // Add to body with entrance animation
    document.body.appendChild(eyeOverlay);

    // Entrance animation for the container
    reminderContainer.style.opacity = '0';
    reminderContainer.style.transform = 'scale(0.9)';
    reminderContainer.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

    setTimeout(() => {
        reminderContainer.style.opacity = '1';
        reminderContainer.style.transform = 'scale(1)';
    }, 10);

    // Remove the overlay after 20 seconds with fade-out effect
    setTimeout(() => {
        reminderContainer.style.opacity = '0';
        reminderContainer.style.transform = 'scale(0.9)';

        setTimeout(() => {
            if (eyeOverlay.parentNode) {
                eyeOverlay.parentNode.removeChild(eyeOverlay);
            }
        }, 500);
    }, 20000);
}


// Handle page load events
function handlePageLoad() {
    // Only process main frame
    if (window.self === window.top) {
        const url = window.location.href;

        // Skip chrome-extension and other internal URLs
        if (url.startsWith('http://') || url.startsWith('https://')) {
            console.log('Content script: Processing URL:', url);
            // Send page load message to background - Background will send GA4 event
            chrome.runtime.sendMessage({
                type: 'PAGE_LOADED',
                url: url
            });
        } else {
            console.log('Content script: Skipping non-web URL:', url);
        }
    }
}

// Wait for document to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', handlePageLoad);
} else {
    handlePageLoad();
}
