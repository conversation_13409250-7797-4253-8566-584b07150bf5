// const SERVER_URL = 'https://smartparent.qubitrhythm.com'; // Original Production URL
const SERVER_URL = 'http://localhost:32410'; // Local Testing URL (HTTP/NodePort)

document.addEventListener('DOMContentLoaded', () => {
    chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'popup_opened' }); // Send GA4 event
    const statusText = document.getElementById('statusText');
    const authenticationSection = document.getElementById('authentication-section');
    const settingsSection = document.querySelector('.settings-section');
    const activateButton = document.getElementById('activateButton');
    const authMessage = document.getElementById('authMessage');
    const userEmailInput = document.getElementById('userEmail'); // Renamed for clarity
    const trialMessage = document.getElementById('trial-message'); // Get trial message element
    const subscribePrompt = document.getElementById('subscribe-prompt'); // Get subscribe prompt element
    const premiumFeaturesSection = document.getElementById('premium-features-section'); // Assuming a container for premium settings
    const freeSettingsSection = document.getElementById('free-settings-section'); // Assuming a container for free settings

    // Status Constants (mirroring backend)
    const STATUS_FREE = 'Free'; // Added
    const STATUS_TRIAL_PENDING_EMAIL = 'TrialPendingEmail'; // Legacy
    const STATUS_TRIAL_ACTIVE = 'TrialActive'; // Premium Trial
    const STATUS_TRIAL_EXPIRED = 'TrialExpired'; // Legacy -> should become Free
    const STATUS_SUBSCRIBED = 'Subscribed'; // Premium

    // Helper function to clear subscription cache (might need update)
    function clearSubscriptionCache(callback) {
        return new Promise((resolve) => {
            // Clear old and new keys
            chrome.storage.sync.remove(['subscribed', 'email', 'userStatus', 'trialUsed', 'inTrial', 'trialExpired'], () => {
                console.log('Subscription cache cleared.');
                if (callback) callback();
                resolve();
            });
        });
    }

    // Add blur event listener for email validation
    userEmailInput.addEventListener('blur', () => { // Use renamed variable
        const email = userEmailInput.value.trim(); // Use renamed variable
        if (email && !validateEmail(email)) {
            chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'email_validation_failed_blur' });
            authMessage.textContent = 'Please input a valid email address';
            authMessage.style.color = 'red';
        } else if (email) { // Only log valid blur if email is not empty
             // Don't log valid email just on blur, log on activation attempt
            authMessage.textContent = '';
        } else {
             // Email field is empty on blur
             authMessage.textContent = '';
        }
    });
    const timerMinutesInput = document.getElementById('timerMinutes');
    const blockingMinutesInput = document.getElementById('blockingMinutes');
    const saveSettingsButton = document.getElementById('saveSettings');
    const eyeProtectionIntervalInput = document.getElementById('eyeProtectionInterval');
    const emailHistoryEnabled = document.getElementById('emailHistoryEnabled');
    const emailHistoryTime = document.getElementById('emailHistoryTime');
    const amPmDisplay = document.getElementById('amPmDisplay');
    const customWhitelistEnabled = document.getElementById('customWhitelistEnabled');
    const whitelistDialog = document.getElementById('whitelistDialog');
    const websiteInput = document.getElementById('websiteInput');
    const addWebsiteBtn = document.getElementById('addWebsite');
    const whitelistEntries = document.querySelector('.whitelist-entries');
    const saveWhitelistBtn = document.getElementById('saveButton');
    const cancelWhitelistBtn = document.getElementById('cancelButton');

    let currentWhitelist = [];

    // Function to update AM/PM display
    function updateAmPmDisplay(timeValue) {
        const hour = parseInt(timeValue.split(':')[0], 10);
        amPmDisplay.textContent = hour >= 12 ? 'PM' : 'AM';
    }

    // Add event listener for time changes
    emailHistoryTime.addEventListener('change', (e) => {
        updateAmPmDisplay(e.target.value);
    });

    // Whitelist Management Functions
    function loadWhitelist() {
        chrome.storage.sync.get(['customWhitelist'], (data) => {
            currentWhitelist = data.customWhitelist || [];
            renderWhitelist();
        });
    }

    function renderWhitelist() {
        whitelistEntries.innerHTML = '';
        currentWhitelist.forEach((url, index) => {
            const entry = document.createElement('div');
            entry.className = 'whitelist-entry';
            entry.innerHTML = `
                <span>${url}</span>
                <button class="delete-btn" data-index="${index}">Delete</button>
            `;
            whitelistEntries.appendChild(entry);
        });

        // Add delete button event listeners
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.dataset.index);
                currentWhitelist.splice(index, 1);
                renderWhitelist();
            });
        });
    }

    function addWebsite() {
        let url = websiteInput.value.trim().toLowerCase();

        // Basic URL validation and formatting
        if (!url) return;

        // Remove http://, https://, and www.
        url = url.replace(/^(https?:\/\/)?(www\.)?/, '');

        // Remove anything after the first slash
        url = url.split('/')[0];

        // Check if URL is already in the list
        if (currentWhitelist.includes(url)) {
            alert('This website is already in your whitelist.');
            return;
        }

        currentWhitelist.push(url);
        websiteInput.value = '';
        renderWhitelist();
    }

    // Add event listener for whitelist checkbox
    customWhitelistEnabled.addEventListener('change', () => {
        if (customWhitelistEnabled.checked) {
            chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'whitelist_dialog_opened' });
            loadWhitelist();
            whitelistDialog.style.display = 'flex';
        } else {
            chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'whitelist_disabled' });
            whitelistDialog.style.display = 'none';
            currentWhitelist = [];
            chrome.runtime.sendMessage({
                type: 'whitelistSettingsChanged',
                enabled: false
            });
        }
    });

    document.querySelector('label[for="customWhitelistEnabled"]').addEventListener('click', () => {
        // This click listener on the label might interfere with the prompt logic
        // Let's rely on the checkbox change event and the container click listener
        // customWhitelistEnabled.checked = !customWhitelistEnabled.checked;
        // customWhitelistEnabled.dispatchEvent(new Event('change'));
    });

    // Add event listeners for whitelist management
    addWebsiteBtn.addEventListener('click', () => {
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'whitelist_add_button_clicked' });
        addWebsite();
    });

    websiteInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
             chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'whitelist_add_enter_pressed' });
            addWebsite();
        }
    });

    saveWhitelistBtn.addEventListener('click', () => {
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'whitelist_save_clicked', eventParams: { count: currentWhitelist.length } });
        chrome.storage.sync.set({
            customWhitelist: currentWhitelist,
            customWhitelistEnabled: true
        }, () => {
            chrome.runtime.sendMessage({
                type: 'whitelistUpdated',
                whitelist: currentWhitelist
            });
            whitelistDialog.style.display = 'none';
        });
    });

    cancelWhitelistBtn.addEventListener('click', () => {
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'whitelist_cancel_clicked' });
        whitelistDialog.style.display = 'none';
        loadWhitelist(); // Reload the original whitelist
        chrome.storage.sync.get(['customWhitelist'], (data) => {
            if (!data.customWhitelist || data.customWhitelist.length === 0) {
                customWhitelistEnabled.checked = false;
                chrome.storage.sync.set({ customWhitelistEnabled: false });
            }
        });
    });

    // --- UI Update Functions ---

    function showLoadingState() {
        statusText.textContent = 'LOADING...';
        authenticationSection.style.display = 'none';
        settingsSection.style.display = 'none';
        trialMessage.style.display = 'none';
        subscribePrompt.style.display = 'none';
    }

    // Renamed and repurposed for the Free state
    function showFreeState(statusData) {
        const storedEmail = statusData.email;
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'ui_state_shown', eventParams: { state_name: 'free' } });
        statusText.textContent = 'STANDARD';
        statusText.classList.remove('subscribed', 'trial', 'error');
        statusText.classList.add('unsubscribed');

        if (!storedEmail) {
            // Still need email activation if not provided
            authenticationSection.style.display = 'block';
            settingsSection.style.display = 'none'; // Hide settings until email provided
            trialMessage.innerHTML = `<p>Please enter your email address and click "Activate" to use SmartParent.</p>`;
            trialMessage.style.display = 'block';
            subscribePrompt.style.display = 'none';
            authMessage.textContent = '';
        } else {
            // Email provided, show settings
            authenticationSection.style.display = 'none';
            settingsSection.style.display = 'block';
            trialMessage.style.display = 'none'; // No trial message for free tier
            // Show subscribe prompt
            subscribePrompt.innerHTML = `
                <p>Upgrade to <a id="subscribeLinkFree" href="#">Premium</a> for more features or <a id="startTrialLinkFree" href="#">start a 7-day trial</a>.</p>
            `;
            subscribePrompt.style.display = 'block';
            setupSubscribeLink('subscribeLinkFree', storedEmail);
            setupStartTrialLink('startTrialLinkFree');

            loadSettings(); // Load settings
            // Disable Premium features specifically
            disablePremiumFeaturesUI(true);
            // Ensure save button reflects Free state (no locking)
            saveSettingsButton.textContent = 'Save Settings';
            saveSettingsButton.style.backgroundColor = '#4CAF50'; // Green
        }
    }

    function showTrialActiveState(statusData) {
        const storedEmail = statusData.email;
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'ui_state_shown', eventParams: { state_name: 'trial_active', remaining_ms: statusData.remainingMs } });
        statusText.textContent = 'TRIAL';
        statusText.classList.remove('subscribed', 'unsubscribed', 'error');
        statusText.classList.add('trial'); // Add trial class for ribbon styling
        authenticationSection.style.display = 'none';
        settingsSection.style.display = 'block';
        trialMessage.innerHTML = `
            <p class="trial-info">Your 7-day Premium trial is active!</p>
            <p id="countdown"></p>
        `;
        trialMessage.style.display = 'block';
        subscribePrompt.innerHTML = `
            <p>To keep Premium features after the trial, please <a id="subscribeLinkTrial" href="#">subscribe</a>.</p>
        `;
        subscribePrompt.style.display = 'block';
        setupSubscribeLink('subscribeLinkTrial', storedEmail);

        loadSettings(); // Load settings
        disablePremiumFeaturesUI(false); // Enable premium features for trial

        // Handle settings lock state (Premium feature)
        chrome.storage.sync.get(['settingsPassword', 'settingsUnlocked'], (data) => {
            if (data.settingsPassword && !data.settingsUnlocked) {
                saveSettingsButton.textContent = 'Unlock';
                saveSettingsButton.style.backgroundColor = 'red';
                lockSettings();
            } else {
                saveSettingsButton.textContent = 'Save & Lock Settings';
                saveSettingsButton.style.backgroundColor = 'green';
                unlockSettings(); // Call without arg to save state
            }
        });

        // Start countdown
        if (statusData.remainingMs > 0) {
            const countdownElement = document.getElementById('countdown');
            if (countdownElement) {
                startCountdown(countdownElement, statusData.remainingMs);
            }
        }
    }

    // No longer needed, handled by showFreeState
    // function showTrialExpiredState(storedEmail) { ... }

    function showSubscribedState(statusData) {
        const storedEmail = statusData.email;
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'ui_state_shown', eventParams: { state_name: 'subscribed' } });
        statusText.textContent = 'PREMIUM';
        statusText.classList.remove('unsubscribed', 'trial', 'error');
        statusText.classList.add('subscribed');
        authenticationSection.style.display = 'none';
        settingsSection.style.display = 'block';
        trialMessage.style.display = 'none';
        subscribePrompt.style.display = 'none';

        loadSettings(); // Load settings
        disablePremiumFeaturesUI(false); // Enable premium features

        // Handle settings lock state (Premium feature)
        chrome.storage.sync.get(['settingsPassword', 'settingsUnlocked'], (data) => {
            if (data.settingsPassword && !data.settingsUnlocked) {
                saveSettingsButton.textContent = 'Unlock';
                saveSettingsButton.style.backgroundColor = 'red';
                lockSettings();
            } else {
                saveSettingsButton.textContent = 'Save & Lock Settings';
                saveSettingsButton.style.backgroundColor = 'green';
                unlockSettings(); // Call without arg to save state
            }
        });
    }

    function showErrorState(errorMsg = 'Error connecting to server.') {
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'ui_state_error_shown', eventParams: { error_message: errorMsg } });
        statusText.textContent = 'ERROR';
        statusText.classList.remove('subscribed', 'unsubscribed', 'trial');
        statusText.classList.add('error');
        authenticationSection.style.display = 'none';
        settingsSection.style.display = 'none';
        trialMessage.innerHTML = `<p style="color:red">${errorMsg}</p>`;
        trialMessage.style.display = 'block';
        subscribePrompt.style.display = 'none';
        // Disable all settings controls on error
        disablePremiumFeaturesUI(true);
        // You might want to disable free features too, or just hide the section
    }

    // Helper to disable/enable Premium UI elements and apply styling
    function disablePremiumFeaturesUI(disable) {
        const premiumSection = document.getElementById('premium-features-section');

        // Whitelist Elements within the section
        const whitelistCheckbox = premiumSection?.querySelector('#customWhitelistEnabled');
        if (whitelistCheckbox) whitelistCheckbox.disabled = disable;

        // Email History Elements within the section
        const emailCheckbox = premiumSection?.querySelector('#emailHistoryEnabled');
        const emailTimeInput = premiumSection?.querySelector('#emailHistoryTime');
        if (emailCheckbox) emailCheckbox.disabled = disable;
        // Disable time input if the main feature is disabled OR if the checkbox is unchecked
        if (emailTimeInput) emailTimeInput.disabled = disable || !emailCheckbox?.checked;

        // Apply a class to the entire premium section for visual indication (e.g., dimming)
        if (premiumSection) {
             premiumSection.classList.toggle('disabled-feature', disable); // Use a more generic class name
        }

        // Password Protection: This is implicitly handled by the Save/Lock button logic.
        // The button's text/state is set based on overall status (Free vs Premium/Trial)
        // and the presence/state of the password in storage.

        // Adjust Save button text and lock state based on overall status
        // We need to fetch the status to make this decision accurately
        chrome.storage.sync.get(['userStatus', 'settingsPassword', 'settingsUnlocked'], (data) => {
            const status = data.userStatus || STATUS_FREE;
            const hasPassword = !!data.settingsPassword;
            const isUnlocked = !!data.settingsUnlocked;

            if (status === STATUS_FREE) {
                saveSettingsButton.textContent = 'Save Settings';
                saveSettingsButton.style.backgroundColor = '#4CAF50'; // Green
                // Ensure settings are visually unlocked in free mode (actual lock doesn't apply)
                unlockSettings(false); // Pass false to avoid saving unlock state
            } else { // Premium or Trial
                if (hasPassword && !isUnlocked) {
                    saveSettingsButton.textContent = 'Unlock';
                    saveSettingsButton.style.backgroundColor = 'red';
                    lockSettings(); // Ensure locked state is applied
                } else {
                    saveSettingsButton.textContent = 'Save & Lock Settings';
                    saveSettingsButton.style.backgroundColor = 'green';
                    unlockSettings(false); // Ensure unlocked state is applied visually
                }
            }
        });


        // If disabling premium features, also hide the whitelist dialog if it's open
        if (disable && whitelistDialog.style.display === 'flex') {
            whitelistDialog.style.display = 'none';
        }
    }

    // Helper to set up subscribe links
    function setupSubscribeLink(linkId, email) {
        const link = document.getElementById(linkId);
        if (link) {
            // Remove existing listener to prevent duplicates
            link.replaceWith(link.cloneNode(true));
            const newLink = document.getElementById(linkId);
            // Add new listener
            newLink.addEventListener('click', (e) => {
                e.preventDefault();
                chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'subscribe_link_clicked', eventParams: { location: linkId } });

                // Check if Alt key is pressed to enable test mode
                const isTestMode = e.altKey;
                const testParam = isTestMode ? '&test=true' : '';

                const subscribeUrl = chrome.runtime.getURL(`subscribe.html?email=${encodeURIComponent(email || '')}${testParam}`);
                chrome.tabs.create({ url: subscribeUrl });

                if (isTestMode) {
                    console.log('Test mode enabled for subscription page');
                }
            });
        }
    }

     // Helper to set up start trial links
     function setupStartTrialLink(linkId) {
         const link = document.getElementById(linkId);
         if (link) {
             // Remove existing listener to prevent duplicates
             link.replaceWith(link.cloneNode(true));
             const newLink = document.getElementById(linkId);
             // Add new listener
             newLink.addEventListener('click', (e) => {
                 e.preventDefault();
                 chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'start_trial_link_clicked', eventParams: { location: linkId } });
                 // Send message to background script to start trial
                 chrome.runtime.sendMessage({ type: 'START_TRIAL' }, (response) => {
                     if (response && response.success) {
                         showConfirmationModal('Premium trial started!');
                         // Re-check status to update UI immediately
                         checkStoredStatus().then(setupPremiumPrompts); // Refresh UI and re-setup prompts
                     } else {
                         showErrorModal(response.error || 'Could not start trial.');
                     }
                 });
             });
         }
     }


    // --- Main Logic ---

    // Fetch status from storage on popup load (updated by background script)
    async function checkStoredStatus() {
        showLoadingState();
        return new Promise((resolve) => { // Return a promise
            chrome.storage.sync.get(['userStatus', 'email', 'trialUsed', 'settingsPassword', 'settingsUnlocked'], (data) => {
                console.log('Popup loaded - Stored status:', data.userStatus, 'Email:', data.email, 'TrialUsed:', data.trialUsed);
                const status = data.userStatus || STATUS_FREE; // Default to Free if undefined
                const email = data.email;
                const trialUsed = data.trialUsed || false;

                // Update UI based on the stored status
                switch (status) {
                    case STATUS_FREE:
                    case STATUS_TRIAL_PENDING_EMAIL: // Treat legacy pending as Free
                    case STATUS_TRIAL_EXPIRED: // Treat legacy expired as Free
                        showFreeState({ email: email, trialUsed: trialUsed }); // Pass data object
                        break;
                    case STATUS_TRIAL_ACTIVE:
                        fetchRemainingTimeAndShowTrial(email, trialUsed); // Fetch fresh data for trial
                        break;
                    case STATUS_SUBSCRIBED:
                        showSubscribedState({ email: email }); // Pass data object
                        break;
                    default:
                        console.warn("Unknown status in storage:", status);
                        showFreeState({ email: email, trialUsed: trialUsed }); // Default to Free state
                        break;
                }

                 // Handle lock state separately after main UI is set
                 if (status === STATUS_TRIAL_ACTIVE || status === STATUS_SUBSCRIBED) {
                     if (data.settingsPassword && !data.settingsUnlocked) {
                         saveSettingsButton.textContent = 'Unlock';
                         saveSettingsButton.style.backgroundColor = 'red';
                         lockSettings(); // Ensure UI reflects locked state
                     } else {
                         saveSettingsButton.textContent = 'Save & Lock Settings';
                         saveSettingsButton.style.backgroundColor = 'green';
                         // unlockSettings(); // unlockSettings is called within loadSettings
                     }
                 } else { // Free state
                     saveSettingsButton.textContent = 'Save Settings';
                     saveSettingsButton.style.backgroundColor = '#4CAF50';
                     unlockSettings(false); // Ensure free settings are editable, don't save state
                     disablePremiumFeaturesUI(true); // Explicitly disable premium UI for free
                 }
                 resolve(); // Resolve the promise once UI is updated
            });
        });
    }

    // Force fresh status check from server and update storage
    async function refreshStatusFromServer() {
        showLoadingState();
        try {
            console.log('Refreshing status from server...');
            const response = await fetch(`${SERVER_URL}/status`);
            if (!response.ok) {
                throw new Error(`Status fetch failed: ${response.status}`);
            }
            const data = await response.json();
            console.log('Fresh status from server:', data);

            // Update storage with fresh data
            const storageUpdate = {
                userStatus: data.status,
                email: data.email || null,
                trialUsed: data.trialUsed || false
            };

            return new Promise((resolve) => {
                chrome.storage.sync.set(storageUpdate, () => {
                    console.log('Storage updated with fresh status:', storageUpdate);
                    // Now call checkStoredStatus to update UI
                    checkStoredStatus().then(resolve);
                });
            });
        } catch (error) {
            console.error('Error refreshing status from server:', error);
            // Fall back to stored status if server fetch fails
            return checkStoredStatus();
        }
    }

    // Helper to fetch remaining time for trial state
    async function fetchRemainingTimeAndShowTrial(email, trialUsed) {
         try {
             // Use /status endpoint which now includes remainingMs
             const response = await fetch(`${SERVER_URL}/status`); // GET request
             if (!response.ok) throw new Error(`Status fetch failed: ${response.status}`);
             const data = await response.json();
             if (data.status === STATUS_TRIAL_ACTIVE) {
                 showTrialActiveState(data); // Pass full data including remainingMs
             } else {
                 // Status changed since last check, revert to appropriate state
                 console.warn("Status changed during fetch, expected TrialActive, got:", data.status);
                 // Update storage with the new status received from backend
                 chrome.storage.sync.set({ userStatus: data.status, email: data.email, trialUsed: data.trialUsed }, () => {
                     checkStoredStatus(); // Re-run based on the updated storage
                 });
             }
         } catch (error) {
             console.error("Error fetching remaining trial time:", error);
             // Show trial state but maybe without countdown? Or show error?
             showTrialActiveState({ email: email, trialUsed: trialUsed, remainingMs: 0 }); // Show trial state without time
             // Optionally show a small error message about fetching time
         }
    }


    // Call status check on load
    checkStoredStatus().then(() => {
         setupPremiumPrompts(); // Setup prompts after initial status check and UI render
    }); // Check stored status first for faster UI update

    // Activation button event listener (calls /activate)
    activateButton.addEventListener('click', async () => {
        const email = userEmailInput.value.trim();

        if (!validateEmail(email)) {
            chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'activation_email_invalid' });
            authMessage.textContent = 'Please enter a valid email address.';
            authMessage.style.color = 'red';
            return;
        }
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'activation_button_clicked' });
        authMessage.textContent = 'Activating...';
        authMessage.style.color = 'black';

        try {
            const response = await fetch(`${SERVER_URL}/activate`, {
                method: 'POST',
                mode: 'cors',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email })
            });

            let data;
            try {
                 data = await response.json();
                 console.log('Backend /activate response:', data);
            } catch (jsonError) {
                 console.error("Failed to parse JSON response from /activate", jsonError);
                 throw new Error(`Activation failed: Server status ${response.status}, invalid response format.`);
            }

            if (!response.ok) {
                 const errorMsg = data.error || `Activation failed: Server status ${response.status}`;
                 throw new Error(errorMsg);
            }

            // Update storage and UI based on activation result
            // Store the single status string, email, and trialUsed flag
            const storageUpdate = {
                userStatus: data.status,
                email: data.email || null,
                trialUsed: data.trialUsed || false // Get trialUsed from response
            };
            chrome.storage.sync.set(storageUpdate, () => {
                console.log('Chrome storage updated after activation:', storageUpdate);
                // Re-check stored status to update UI correctly
                checkStoredStatus().then(() => {
                    setupPremiumPrompts(); // Re-setup prompts after UI update
                });
                // Show confirmation based on the NEW status
                if (data.status === STATUS_FREE) {
                     showConfirmationModal('Email activated successfully! You are using the Free plan.');
                } else if (data.status === STATUS_SUBSCRIBED) {
                     chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'activation_success_subscribed' });
                     showConfirmationModal('Subscription activated successfully!');
                } else if (data.status === STATUS_TRIAL_ACTIVE) {
                     // This case shouldn't happen with the new flow where /activate doesn't start trial
                     console.warn("Unexpected TrialActive status returned from /activate");
                     showConfirmationModal('Activation successful.'); // Generic message
                } else {
                     // Handle other potential statuses if necessary
                     showConfirmationModal('Activation processed.');
                }
            });

        } catch (error) {
            console.error('Error during /activate call:', error);
            chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'activation_failed', eventParams: { error_message: error.message } });
            authMessage.textContent = `Activation error: ${error.message}`;
            authMessage.style.color = 'red';
             // Optionally revert UI to a known state, e.g., Free without email
             showFreeState({ email: null }); // Revert UI
        }
    });

    // Save settings event listener
    saveSettingsButton.addEventListener('click', async (event) => { // Added event parameter
        // Check if the premium prompt should be shown (moved from setupPremiumPrompts)
        const storageData = await new Promise(resolve => chrome.storage.sync.get(['userStatus', 'settingsPassword', 'email'], resolve)); // Added email here
        if (storageData.userStatus === STATUS_FREE && !storageData.settingsPassword && saveSettingsButton.textContent !== 'Unlock') {
             event.preventDefault(); // Prevent default button action
             event.stopPropagation();
             console.log('Password setting blocked for Free user.');
             showPremiumPromptModal(storageData.email);
             return; // Stop further execution of this listener
        }

        if (saveSettingsButton.textContent === 'Unlock') {
            chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'settings_unlock_button_clicked' });
            // Prompt user for password to unlock
            chrome.storage.sync.get(['settingsPassword'], (res) => {
                if (!res.settingsPassword) {
                    // This case should ideally not be reachable if premium prompt works
                    showErrorModal("Cannot unlock: No password is set.");
                    return;
                }
                const enteredPwd = prompt('Enter your SmartParent password:');
                if (!enteredPwd) {
                    return; // Do nothing if user clicks Cancel
                }
                if (verifyPassword(enteredPwd, res.settingsPassword)) {
                    chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'settings_unlocked_success' });
                    unlockSettings(); // Unlock and save state
                } else {
                     chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'settings_unlock_failed_password' });
                    showErrorModal("Wrong password. Please find the password from your inbox with subject 'Your password of SmartParent.'");
                }
            });
            return; // Stop here if unlocking
        }

        // Proceed with saving settings
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'settings_save_lock_button_clicked' });
        const timerMinutes = parseInt(timerMinutesInput.value, 10);
        const blockingMinutes = parseInt(blockingMinutesInput.value, 10);
        const eyeProtectionInterval = parseInt(eyeProtectionIntervalInput.value, 10);

        if (isNaN(timerMinutes) || isNaN(blockingMinutes) || isNaN(eyeProtectionInterval) ||
            timerMinutes <= 0 || blockingMinutes <= 0 || eyeProtectionInterval <= 0) {
            alert('Please enter valid numbers for all settings.');
            return;
        }

        // Check status and password state again for locking logic
        chrome.storage.sync.get(['userStatus', 'settingsPassword', 'email'], (res) => {
            const status = res.userStatus || STATUS_FREE;
            const hasPassword = !!res.settingsPassword;
            const userEmail = res.email;

            const saveCoreSettings = (callback) => {
                chrome.storage.sync.set({
                    timerMinutes,
                    blockingMinutes,
                    eyeProtectionInterval,
                    // Only save premium settings if user is premium/trial
                    ...(status !== STATUS_FREE && {
                        emailHistoryEnabled: emailHistoryEnabled.checked,
                        emailHistoryTime: emailHistoryTime.value,
                        customWhitelistEnabled: customWhitelistEnabled.checked
                    })
                }, () => {
                    // Notify background script about settings changes
                    // Only send premium changes if user is premium/trial
                    if (status !== STATUS_FREE) {
                        chrome.runtime.sendMessage({
                            type: 'emailHistorySettingsChanged',
                            enabled: emailHistoryEnabled.checked,
                            time: emailHistoryTime.value
                        });
                        chrome.runtime.sendMessage({
                            type: 'whitelistSettingsChanged',
                            enabled: customWhitelistEnabled.checked
                        });
                    }
                     chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'settings_saved', eventParams: {
                         timer_minutes: timerMinutes,
                         blocking_minutes: blockingMinutes,
                         eye_interval: eyeProtectionInterval,
                         // Conditionally include premium params
                         ...(status !== STATUS_FREE && {
                             email_history: emailHistoryEnabled.checked,
                             whitelist_enabled: customWhitelistEnabled.checked
                         })
                     }});
                     if (callback) callback();
                });
            };

            const lockAndConfirm = (newPassword = null) => {
                lockSettings();
                if (newPassword) {
                    sendPasswordEmail(newPassword, userEmail);
                    showConfirmationModal('Password has been sent to your email address. Settings have been saved and locked.');
                } else {
                    showConfirmationModal('Settings have been saved and locked.');
                }
                saveSettingsButton.textContent = 'Unlock';
                saveSettingsButton.style.backgroundColor = 'red';
            };

            if (status === STATUS_FREE) {
                // Free users just save, no locking
                saveCoreSettings(() => {
                    showConfirmationModal('Settings saved.');
                });
            } else { // Premium or Trial users
                if (!hasPassword) {
                    // Premium/Trial user, no password set -> prompt to create
                    chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'settings_password_create_prompted' });
                    const pwd = prompt('Create a new password to lock these settings:');
                    if (pwd) {
                        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'settings_password_created' });
                        const hashed = hashPassword(pwd);
                        chrome.storage.sync.set({ settingsPassword: hashed }, () => {
                            saveCoreSettings(() => lockAndConfirm(pwd)); // Save then lock
                        });
                    } else {
                        // User cancelled password creation, just save settings without locking
                        saveCoreSettings(() => {
                            showConfirmationModal('Settings saved (password not set).');
                        });
                    }
                } else {
                    // Premium/Trial user, password exists -> save and lock
                    saveCoreSettings(() => lockAndConfirm());
                }
            }
        });
    });

    // Attempt to unlock on input focus if locked (Only for Premium/Trial users)
    [timerMinutesInput, blockingMinutesInput, eyeProtectionIntervalInput, emailHistoryTime, customWhitelistEnabled, emailHistoryEnabled].forEach(input => {
        if (!input) return; // Skip if element doesn't exist
        input.addEventListener('focus', () => {
            chrome.storage.sync.get(['userStatus', 'settingsPassword', 'settingsUnlocked'], (res) => {
                // Only prompt if Premium/Trial, password exists, settings are locked, and the input is currently disabled
                if ((res.userStatus === STATUS_TRIAL_ACTIVE || res.userStatus === STATUS_SUBSCRIBED) &&
                    res.settingsPassword && !res.settingsUnlocked && input.disabled)
                {
                    const enteredPwd = prompt('Enter your SmartParent password to change settings:');
                    if (!enteredPwd) {
                        return; // Do nothing if user clicks Cancel
                    }
                    if (verifyPassword(enteredPwd, res.settingsPassword)) {
                        unlockSettings(); // Unlock and save state
                    } else {
                        showErrorModal("Wrong password. Please find the password from your inbox with subject 'Your password of SmartParent.'");
                    }
                }
            });
        });
    });


    // Add event listener for checkbox to enable/disable time input (only if not disabled)
    emailHistoryEnabled.addEventListener('change', () => {
        // Check if the checkbox itself is disabled (meaning user is Free)
        if (!emailHistoryEnabled.disabled) {
            emailHistoryTime.disabled = !emailHistoryEnabled.checked;
        }
    });

    function loadSettings() {
        chrome.storage.sync.get([
            'timerMinutes', 'blockingMinutes', 'eyeProtectionInterval', // Free settings
            'emailHistoryEnabled', 'emailHistoryTime', 'customWhitelistEnabled', 'customWhitelist', // Premium settings
            'settingsPassword', 'settingsUnlocked' // Lock state
        ], (data) => {
            // Load Free settings
            timerMinutesInput.value = data.timerMinutes || 15;
            blockingMinutesInput.value = data.blockingMinutes || 60;
            eyeProtectionIntervalInput.value = data.eyeProtectionInterval || 30;

            // Load Premium settings (values loaded regardless of status, disabling handled separately)
            emailHistoryEnabled.checked = data.emailHistoryEnabled || false;
            const timeValue = data.emailHistoryTime || '00:00';
            emailHistoryTime.value = timeValue;
            updateAmPmDisplay(timeValue);
            customWhitelistEnabled.checked = data.customWhitelistEnabled || false;

            // Load whitelist content if enabled
            if (customWhitelistEnabled.checked) {
                currentWhitelist = data.customWhitelist || [];
                renderWhitelist(); // Render the list
            } else {
                currentWhitelist = []; // Ensure local list is empty if disabled
            }

            // Initial UI state based on lock/unlock is handled by checkStoredStatus calling disablePremiumFeaturesUI
        });
    }

    // Utility function to validate email format
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(String(email).toLowerCase());
    }

    // Helper function to lock
    function lockSettings() {
        // Disable Free settings
        timerMinutesInput.disabled = true;
        blockingMinutesInput.disabled = true;
        eyeProtectionIntervalInput.disabled = true;
        // Disable Premium settings
        emailHistoryTime.disabled = true;
        emailHistoryEnabled.disabled = true;
        customWhitelistEnabled.disabled = true;
        // Hide whitelist dialog if open
        if (whitelistDialog.style.display === 'flex') {
            whitelistDialog.style.display = 'none';
        }
        // Update storage and button
        chrome.storage.sync.set({ settingsUnlocked: false });
        saveSettingsButton.textContent = 'Unlock';
        saveSettingsButton.style.backgroundColor = 'red';
    }

    // Helper function to unlock (add optional flag to prevent saving state)
    function unlockSettings(saveState = true) {
        // Enable Free settings
        timerMinutesInput.disabled = false;
        blockingMinutesInput.disabled = false;
        eyeProtectionIntervalInput.disabled = false;
        // Enable Premium settings (actual state depends on disablePremiumFeaturesUI)
        emailHistoryEnabled.disabled = false;
        emailHistoryTime.disabled = !emailHistoryEnabled.checked; // Conditionally enable time
        customWhitelistEnabled.disabled = false;
        // Update button and storage (if requested)
        saveSettingsButton.textContent = 'Save & Lock Settings';
        saveSettingsButton.style.backgroundColor = 'green';
        if (saveState) {
            chrome.storage.sync.set({ settingsUnlocked: true });
        }
    }

    // Simple hashing (demo; replace with a real hash if needed)
    function hashPassword(password) {
        return btoa(password);
    }

    function verifyPassword(plain, hashed) {
        return btoa(plain) === hashed;
    }

    function sendPasswordEmail(password, email) {
        if (!email) return;
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'settings_password_email_sent' }); // Don't send email PII
        fetch(`${SERVER_URL}/send-password-email`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password })
    }).catch(err => console.error('Error sending password email:', err));
}

    // --- Add Premium Feature Prompt Logic ---
    function setupPremiumPrompts() {
        const premiumSection = document.getElementById('premium-features-section');
        if (!premiumSection) {
            console.error("Premium features section not found in HTML.");
            return;
        }

        // Add listener to the entire premium section container
        premiumSection.addEventListener('click', (event) => {
            // Check if the user is Free
            chrome.storage.sync.get(['userStatus', 'email'], (data) => {
                if (data.userStatus === STATUS_FREE) {
                    // Check if the clicked element itself or its parent is interactive (button, input, label)
                    // AND is actually within the premium section (to avoid triggering on clicks in gaps)
                    let target = event.target;
                    let isInteractivePremiumElement = false;
                    while(target && target !== document.body) { // Traverse up to body
                        if (target === premiumSection) { // Found the section boundary
                             // Now check if the original target was interactive
                             if (event.target.tagName === 'BUTTON' || event.target.tagName === 'INPUT' || event.target.tagName === 'LABEL') {
                                 isInteractivePremiumElement = true;
                             }
                             break; // Stop traversal
                        }
                        target = target.parentElement;
                    }


                    if (isInteractivePremiumElement) {
                        event.preventDefault(); // Stop default action (e.g., checking box)
                        event.stopPropagation(); // Stop event bubbling
                        console.log('Premium feature interaction blocked for Free user.');
                        showPremiumPromptModal(data.email); // Show the upgrade/trial modal
                    }
                }
                // If TrialActive or Subscribed, do nothing, allow the event to proceed
            });
        }, true); // Use capture phase to intercept clicks early

        // Special handling for the Save/Lock button when trying to set a password in Free mode
        // This listener needs to run *before* the main save listener if we want to prevent it.
        saveSettingsButton.addEventListener('click', (event) => {
            // Check if trying to lock (which implies setting/using password) while Free
            chrome.storage.sync.get(['userStatus', 'settingsPassword', 'email'], (data) => { // Added email
                // Show prompt if user is Free AND no password exists yet (as clicking Save would trigger password creation)
                if (data.userStatus === STATUS_FREE && !data.settingsPassword) {
                    // Prevent the default save/lock action defined later
                    event.preventDefault();
                    event.stopPropagation(); // Crucial to stop the other listener
                    console.log('Password setting blocked for Free user.');
                    showPremiumPromptModal(data.email);
                }
                // If user is Free but password *already* exists (shouldn't happen with gating, but safety check),
                // or if user is Premium/Trial, this listener does nothing, and the main save listener proceeds.
            });
        }, true); // Use capture phase
    }

    // Function to show the Premium prompt modal
    function showPremiumPromptModal(email) {
        chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'premium_prompt_shown' });
        // Reuse confirmation modal structure or create a dedicated one
        const modal = document.getElementById('confirmationModal'); // Reuse existing modal
        const messageElement = document.getElementById('confirmationMessage');

        if (!modal || !messageElement) {
            showErrorModal("UI Error: Modal not found."); // Use error modal for UI issues
            return;
        }

        // Customize message and add buttons
        messageElement.innerHTML = `
            This is a Premium feature. Please upgrade or start a free trial.
            <div class="modal-buttons">
                <button id="modalStartTrialBtn">Start 7-Day Trial</button>
                <button id="modalSubscribeBtn">Subscribe Now</button>
                <button id="modalCancelBtn">Cancel</button> <!-- Added Cancel -->
            </div>
        `;
        messageElement.style.color = ''; // Reset color if previously error

        modal.classList.add('show');
        modal.style.display = 'flex';

        // --- Add listeners to modal buttons ---
        // Remove previous listeners before adding new ones to prevent duplicates
        const startTrialBtn = document.getElementById('modalStartTrialBtn');
        const subscribeBtn = document.getElementById('modalSubscribeBtn');
        const cancelBtn = document.getElementById('modalCancelBtn'); // Get cancel button

        const newStartTrialBtn = startTrialBtn.cloneNode(true);
        startTrialBtn.parentNode.replaceChild(newStartTrialBtn, startTrialBtn);

        const newSubscribeBtn = subscribeBtn.cloneNode(true);
        subscribeBtn.parentNode.replaceChild(newSubscribeBtn, subscribeBtn);

        const newCancelBtn = cancelBtn.cloneNode(true); // Clone cancel button
        cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn); // Replace cancel button

        // Add new listeners
        newStartTrialBtn.addEventListener('click', () => {
             chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'premium_prompt_start_trial_clicked' });
             chrome.runtime.sendMessage({ type: 'START_TRIAL' }, (response) => {
                 modal.classList.remove('show');
                 setTimeout(() => modal.style.display = 'none', 300); // Hide modal

                 // Always refresh status from server after trial attempt, regardless of response
                 // This ensures UI is consistent with actual server state
                 refreshStatusFromServer().then(() => {
                     setupPremiumPrompts(); // Re-setup prompts after status refresh

                     // Show appropriate message based on response
                     if (response && response.success) {
                         showConfirmationModal('Premium trial started!');
                     } else {
                         // Check if trial actually started despite error response
                         chrome.storage.sync.get(['userStatus'], (data) => {
                             if (data.userStatus === 'TrialActive') {
                                 showConfirmationModal('Premium trial started!');
                             } else {
                                 showErrorModal(response.error || 'Could not start trial. Please try again.');
                             }
                         });
                     }
                 });
             });
        });

        newSubscribeBtn.addEventListener('click', () => {
             chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'premium_prompt_subscribe_clicked' });
             const subscribeUrl = chrome.runtime.getURL(`subscribe.html?email=${encodeURIComponent(email || '')}`);
             chrome.tabs.create({ url: subscribeUrl });
             modal.classList.remove('show');
             setTimeout(() => modal.style.display = 'none', 300); // Hide modal
        });

        newCancelBtn.addEventListener('click', () => { // Add listener for cancel
             chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'premium_prompt_cancel_clicked' });
             modal.classList.remove('show');
             setTimeout(() => modal.style.display = 'none', 300); // Hide modal
        });
    }

    // Call setupPremiumPrompts after initial UI setup
    checkStoredStatus().then(() => {
         setupPremiumPrompts(); // Setup prompts after initial status check and UI render
    });

    // Handle Contact Support link click
    const contactSupportLink = document.getElementById('contactSupportLink');
    if (contactSupportLink) {
        contactSupportLink.addEventListener('click', async (e) => {
            e.preventDefault();
            chrome.runtime.sendMessage({ type: 'GA4_EVENT', eventName: 'contact_support_clicked' });

            const supportEmail = '<EMAIL>';

            try {
                // Use the modern Clipboard API
                await navigator.clipboard.writeText(supportEmail);
                showConfirmationModal(`Support email copied to clipboard: ${supportEmail}`);
            } catch (err) {
                // Fallback for browsers that don't support Clipboard API
                showConfirmationModal(`Please contact us at: ${supportEmail}`);
                console.error('Failed to copy email: ', err);
            }
        });
    }

}); // End DOMContentLoaded

// --- Countdown Timer Function ---
let countdownInterval = null; // Keep track of the interval

function startCountdown(element, remainingMs) {
    if (countdownInterval) {
        clearInterval(countdownInterval); // Clear existing interval if any
    }

    if (!element) {
        console.error("Cannot start countdown: element is null");
        return;
    }

    let remainingSeconds = Math.max(0, Math.floor(remainingMs / 1000));

    function updateCountdown() {
        if (remainingSeconds <= 0) {
            element.textContent = 'Trial expired.';
            clearInterval(countdownInterval);
            // Optionally trigger a status refresh here
            checkStoredStatus().then(setupPremiumPrompts); // Refresh UI on expiry
            return;
        }

        const days = Math.floor(remainingSeconds / (24 * 60 * 60));
        const hours = Math.floor((remainingSeconds % (24 * 60 * 60)) / (60 * 60));
         const minutes = Math.floor((remainingSeconds % (60 * 60)) / 60);
         const seconds = remainingSeconds % 60;

         // Use innerHTML to apply different styles via spans
         const prefix = `<span class="countdown-prefix">Remaining days: </span>`;
         const timeValue = `<span class="countdown-time">${days} Day${days !== 1 ? 's' : ''} - ${String(hours).padStart(2, '0')}h${String(minutes).padStart(2, '0')}m${String(seconds).padStart(2, '0')}s</span>`;
         element.innerHTML = prefix + timeValue;

         remainingSeconds--;
    }

    updateCountdown(); // Initial display
    countdownInterval = setInterval(updateCountdown, 1000); // Update every second
}
// Removed unused checkTrialStatus function

function showConfirmationModal(message) {
    const modal = document.getElementById('confirmationModal');
    const messageElement = document.getElementById('confirmationMessage');

    if (!modal || !messageElement) {
        console.error('Modal elements not found in the DOM.');
        return;
    }

    messageElement.textContent = message;
    modal.classList.add('show');
    modal.style.display = 'flex';

    // Automatically hide the modal after 3 seconds
    setTimeout(() => {
        modal.classList.remove('show');
        // Allow transition to complete before hiding
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }, 3000);
}

function showErrorModal(message) {
    const modal = document.getElementById('confirmationModal');
    const messageElement = document.getElementById('confirmationMessage');
    if (!modal || !messageElement) {
        console.error('Modal elements not found in the DOM.');
        return;
    }
    messageElement.textContent = message;
    messageElement.style.color = 'red';
    modal.classList.add('show');
    modal.style.display = 'flex';
    // Automatically hide after 3 seconds
    setTimeout(() => {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
            messageElement.style.color = '';
        }, 300);
    }, 3000);
}
