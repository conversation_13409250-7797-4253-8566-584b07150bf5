# SmartParent Extension (Production Version)

This directory contains the production version of the SmartParent Chrome extension configured for the production environment. This version is designed to work with the production server at `https://smartparent.qubitrhythm.com`.

## Overview

The production extension:
- Uses the production server URL (`https://smartparent.qubitrhythm.com`)
- Has the official name "Smart Parental Control"
- Has the version number "2.1.0" to match the existing production version
- Includes permissions for the production server URL
- Uses production price IDs for Stripe subscriptions

## Key Differences from Test-Production

1. **Server URL**: Uses `https://smartparent.qubitrhythm.com` instead of `http://*************` or `https://test.qubitrhythm.com`
2. **Extension Name**: Uses "Smart Parental Control" instead of "Smart Parental Control (Test-Prod)"
3. **Version Number**: Uses "2.1.0" instead of "2.1.1"
4. **Permissions**: Includes only the necessary permissions for the production server URL
5. **Content Security Policy**: Includes only the necessary URLs for the production environment
6. **Price IDs**: Uses production price IDs:
   - Monthly ($2.99): `price_1RP98oAR7VlUIrExatqrxAEE`
   - Annual ($29.99): `price_1RP99PAR7VlUIrExQiPIcgZl`

## Installation

To install the production extension:

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top-right corner
3. Click "Load unpacked" and select the `smartparent-extension-prod` directory
4. The extension will be installed with the name "Smart Parental Control"

## Testing

When testing the production extension:

1. The extension will automatically connect to the production server at `https://smartparent.qubitrhythm.com`
2. All API calls will be made to the production server
3. The extension will use production price IDs for Stripe subscriptions

## Files Modified for Production

The following files have been modified for the production environment:

- `manifest.json`: Updated name, version, permissions, and content security policy
- `background.js`: Updated SERVER_URL to use the production server URL
- `popup/popup.js`: Updated SERVER_URL to use the production server URL
- `subscribe.js`: Updated to use production server URL and price IDs

## Publishing to Chrome Web Store

To publish the production extension to the Chrome Web Store:

1. Verify that all features work correctly in the production environment
2. Create a ZIP file of the `smartparent-extension-prod` directory
3. Upload the ZIP file to the Chrome Web Store Developer Dashboard
4. Submit the extension for review

## Backward Compatibility

The production extension is designed to be backward compatible with the existing production server. Users of the old version (v2.1.0) will be able to update to this version without any issues.
